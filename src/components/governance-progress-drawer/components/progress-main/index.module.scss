.node-risk-drawer-header {
  height: 30px;
  color: #000;
  font-size: 12px;

  .node-risk-drawer-header-number {
    color: #000;
    font-size: 14px;
    font-weight: 500;
  }

  .node-risk-drawer-header-jump {
    color: #006eff;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
  }
}

.download-report-button {
  display: flex;
  height: 20px;
  align-items: center;
  color: #006eff;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;

  &>span {
    display: flex;
    align-items: center;
  }

  .download-report-button-inner-disabled {
    color: rgba(0, 0, 0, .40);

    .download-svg {
      path: {
        fill: rgba(0, 0, 0, .40) !important;
      }

      :global {
        path {
          fill: rgba(0, 0, 0, .40) !important;
        }
      }
    }
  }

  .haschange-tips {
    margin-left: 5px;
    color: rgba(0, 0, 0, .40);
    font-size: 14px;
  }
}

.node-risk-tabs {
  height: calc(100% - 88px);
  margin-top: 58px;

  :global {
    .sdk-cloud-inspection-tabs {
      height: 100%;

      .sdk-cloud-inspection-tabs__tabpanel {
        height: calc(100% - 30px);
      }
    }

    .status-tip-fixed {
      width: 100% !important;
    }
  }
}

.node-risk-drawer-content {
  width: 100%;
  height: 100%;
}

.node-risk-drawer {
  :global {
    .sdk-cloud-inspection-drawer__body {
      .sdk-cloud-inspection-drawer__body-inner {
        display: flex;
        height: 100%;
        flex-direction: column;
      }
    }
  }
}

.node-risk-drawer-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 700;

  .node-risk-drawer-title-text {
    overflow: hidden;
    max-width: 180px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .chat-bi-trriger {
    position: relative;
    top: 2px;
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
  }
}
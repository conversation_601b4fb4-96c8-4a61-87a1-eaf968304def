import React, {
  useEffect, useState, useMemo,
} from 'react';
import { processSearchBoxValues } from '@src/utils';
import { TagSearchBox } from '@tencent/tea-component';
import { getTagKeysHandle } from '@src/service/common-service/final-request';
import { useGlobalSelector } from '@src/store/global/index';
import { describeGroupAndProductInfosHandle } from '@src/service';
import { t } from '@tea/app/i18n';
import { app } from '@tea/app';
import { describeSubAccountsByMainAccountHandle } from '@src/service/node-risk-drawer/final-request';
import TagPicker from '../tag-picker';
import s from './index.module.scss';

interface ITableSearchBoxProps {
  value: any;
  onChange: (value: any) => void;
  productListCallback: (value: any) => void;
}
/**
 * 关键字检索组件
 * @returns
 */
export default function TableSearchBox(props: ITableSearchBoxProps): React.ReactElement {
  const {
    value,
    onChange,
    productListCallback,
  } = props;
  const { pluginPropsData } = useGlobalSelector();
  const [tagKeys, setTagKeys] = useState<string[]>([]);
  const [productList, setProductList] = useState([]);
  const [accountList, setAccountList] = useState([]);

  const attributesMemo = useMemo(() => {
    const attributes: any = [
      {
        type: ['multiple', { searchable: true }],
        key: 'ClaimPerson',
        name: '跟进人',
        values: accountList?.map((item) => ({
          key: item.Name,
          name: item.Name,
        })),
      },
      {
        type: ['multiple', { searchable: true }],
        key: 'Product',
        name: '云产品',
        values: productList,
      },
      {
        type: 'input',
        key: 'InsId',
        name: '实例ID',
      },
      {
        type: 'input',
        key: 'Name',
        name: '实例名称',
      },
      {
        type: 'multiple',
        key: 'Level',
        name: '风险等级',
        values: () => [
          { key: 2, name: '中风险' },
          { key: 3, name: '高风险' },
        ],
      },
      {
        type: ['multiple', { all: true, searchable: true }],
        key: 'TagName',
        name: '标签键',
        values: () => tagKeys.map((item) => ({ key: item, name: item })),
      },
      {
        type: 'render',
        key: 'TagList',
        name: '标签',
        render: ({ onSelect }) => (
          <div id="tag-render-overlay" className={s['tag-render-overlay']}>
            <TagPicker
              setPreviewParams={(value) => {
                const tagData = [];
                value.forEach((valueItem) => {
                  valueItem.TagSlice?.forEach((tagItem) => {
                    tagData.push({
                      key: tagItem.Key,
                      name: Array.isArray(tagItem.Value) ? tagItem.Value.join(';') : '',
                    });
                  });
                });
                onSelect(tagData);
              }}
            />
          </div>
        ),
      },
    ];

    return attributes;
  }, [tagKeys, productList, accountList]);

  // 获取产品和维度信息
  const getProductsGroupsInfo = async () => {
    try {
      const res = await describeGroupAndProductInfosHandle({
        env: pluginPropsData.env,
        uin: pluginPropsData.uin,
      });
      if (!res) {
        return;
      }
      const tmp = [];
      const productDict = {};
      res.Products.forEach((item) => {
        productDict[item.Product] = item.Name;
        tmp.push({ key: item.Product, name: item.Name });
      });
      productListCallback?.(tmp);
      setProductList(tmp);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{msg}}', { msg }));
    }
  };
  const getSubAccountsByMainAccount = () => {
    describeSubAccountsByMainAccountHandle({
      env: pluginPropsData.env,
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setAccountList(rs.SubAccountInfoList);
      }
    });
  };

  useEffect(() => {
    getTagKeysHandle({
      env: pluginPropsData.env,
      apiParams: {
        data: {
          Language: 'zh-CN',
          MaxResults: 1000,
          PaginationToken: '',
        },
      },
      uin: pluginPropsData.uin,
    }).then((rs) => {
      if (rs) {
        setTagKeys(rs.TagKeys);
      }
    });
    getProductsGroupsInfo();
    getSubAccountsByMainAccount();
  }, []);

  return (
    <TagSearchBox
      attributes={attributesMemo}
      minWidth={400}
      style={{ width: '400px' }}
      value={value}
      onChange={(values) => {
        const processedValues = processSearchBoxValues(values);
        const valuesFilter = processedValues?.filter((item: any) => !!item?.attr);
        onChange(valuesFilter || []);
      }}
    />
  );
}

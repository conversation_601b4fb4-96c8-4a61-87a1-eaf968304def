import React, { useEffect } from 'react';
import { useGlobalSelector } from '@src/store/global/index';
import ShapeNumberBar from '@src/components/shape-number-bar';

interface IuseShapesBarProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

/**
 * 图元角标及架构图顶栏hook
 * @returns
 */
export default function useShapesBar(props: IuseShapesBarProps) {
  const {
    pluginAPI,
  } = props;
  const {
    createBar,
  } = pluginAPI;
  const {
    archScanRiskInfo, riskFilterType, pluginPropsData, inspectionTaskData,
  } = useGlobalSelector();
  const createShapesBarHandle = () => {
    const { NodeRiskItems: nodeRiskItems } = archScanRiskInfo;
    const { nodeList } = pluginPropsData.archInfo;
    nodeRiskItems.forEach((item) => {
      const archNodeInfo = nodeList.find((node) => node.DiagramId === item.NodeUuid);
      if (archNodeInfo?.hasResource) {
        // 绑定资源的才进行角标操作
        const {
          HighRiskCount: highRiskCount,
          MediumRiskCount: mediumRiskCount,
          NodeUuid: nodeUuid,
          LastSuccessTaskId: lastSuccessTaskId,
          LastTaskId: lastTaskId,
        } = item;
        const nodeTaskStatusItem = inspectionTaskData.NodeTaskStatusList
          .find((nodeTaskStatusItem) => nodeTaskStatusItem.NodeUuid === nodeUuid);
        createBar([nodeUuid], {
          children: React.createElement(ShapeNumberBar, {
            highRiskCount,
            mediumRiskCount,
            riskFilterType,
            nodeTaskStatus: nodeTaskStatusItem,
            archTaskStatus: inspectionTaskData?.IsFinish,
            hasTask: !!(lastSuccessTaskId && lastTaskId),
          }),
        });
      }
    });
  };

  useEffect(() => {
    if (archScanRiskInfo?.NodeRiskItems?.length && pluginPropsData?.archInfo?.nodeList) {
      createShapesBarHandle();
    }
  }, [archScanRiskInfo, riskFilterType, pluginPropsData, inspectionTaskData]);

  return null;
}

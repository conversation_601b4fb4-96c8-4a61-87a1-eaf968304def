/* eslint-disable max-len */
/* eslint-disable import/prefer-default-export */
import { createServiceFunc, ResultCommonType } from '@src/utils/request';

import {
  ICreateSubscriptionEmailV2Params,
  IDescribeRiskInstancesInMapParams,
  IDescribeRiskInstancesInMapResult,
  IupdateInstanceToClaimedStatusInMapParams,
  IUpdateInstanceToIgnoredStatusInMapParams,
  IUpdateSubscriptionEmailV2Params,
  IDescribeRiskManageSubjectListParams,
  IDescribeRiskManageSubjectListResponse,
  IDescribeRiskManageInstanceListParams,
  IDescribeRiskManageInstanceListResponse,
  ICreateRiskManageInstanceParams,
  IUpdateRiskManageSubjectParams,
  IUpdateRiskManageSubjectResponse,
  IDeleteRiskManageSubjectParams,
  IDescribeInsightMessageParams,
  IDescribeInsightMessageResponse,
  IDescribeArchSvgDataParams,
  IDescribeArchSvgDataResponse,
} from './index.type';

/**
 * @description 查询架构图下存在风险的实例
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskInstancesInNode
 */
export const describeRiskInstancesInMap: (params: IDescribeRiskInstancesInMapParams) =>
Promise<ResultCommonType<IDescribeRiskInstancesInMapResult>> = createServiceFunc('DescribeRiskInstancesInMap');

/**
 * @description 创建报告订阅邮箱
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskInstancesInNode
 */
export const createSubscriptionEmailV2: (params: ICreateSubscriptionEmailV2Params) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('CreateSubscriptionEmailV2');

/**
 * @description 创建报告订阅邮箱
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskInstancesInNode
 */
export const updateSubscriptionEmailV2: (params: IUpdateSubscriptionEmailV2Params) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('UpdateSubscriptionEmailV2');

/**
 * @description 架构图维度实例变成已认领状态
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskInstancesInNode
 */
export const updateInstanceToClaimedStatusInMap: (params: IupdateInstanceToClaimedStatusInMapParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('UpdateInstanceToClaimedStatusInMap');

/**
 * @description 架构图维度实例修改成已忽略状态
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskInstancesInNode
 */
export const updateInstanceToIgnoredStatusInMap: (params: IUpdateInstanceToIgnoredStatusInMapParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('UpdateInstanceToIgnoredStatusInMap');

/**
 * @description 查询治理主题
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskManageSubjectList
 */
export const describeRiskManageSubjectList: (params: IDescribeRiskManageSubjectListParams) =>
Promise<ResultCommonType<IDescribeRiskManageSubjectListResponse>> = createServiceFunc('DescribeRiskManageSubjectList');

/**
 * @description 分页查询治理主题风险实例
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeRiskManageInstanceList
 */
export const describeRiskManageInstanceList: (params: IDescribeRiskManageInstanceListParams) =>
Promise<ResultCommonType<IDescribeRiskManageInstanceListResponse>> = createServiceFunc('DescribeRiskManageInstanceList');

/**
 * @description 保存巡检风险治理实例信息
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=CreateRiskManageInstance
 */
export const createRiskManageInstance: (params: ICreateRiskManageInstanceParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('CreateRiskManageInstance');

/**
 * @description 保存巡检风险治理信息
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=UpdateRiskManageSubject
 */
export const updateRiskManageSubject: (params: IUpdateRiskManageSubjectParams) =>
Promise<ResultCommonType<IUpdateRiskManageSubjectResponse>> = createServiceFunc('UpdateRiskManageSubject');

/**
 * @description 删除巡检风险治理信息
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DeleteRiskManageSubject
 */
export const deleteRiskManageSubject: (params: IDeleteRiskManageSubjectParams) =>
Promise<ResultCommonType<unknown>> = createServiceFunc('DeleteRiskManageSubject');

/**
 * @description 获取洞察对话消息内容
 * <AUTHOR>
 * @url https://capi.woa.com/api/detail?product=advisor&env=api_pre_release&version=2020-07-21&action=DescribeInsightMessage
 */
export const describeInsightMessage: (params: IDescribeInsightMessageParams) =>
Promise<ResultCommonType<IDescribeInsightMessageResponse>> = createServiceFunc('DescribeInsightMessage');

/**
 * @description 查询架构图SVG数据
 * <AUTHOR> @url https://capi.woa.com/api/detail?product=advisor&version=2020-07-21&action=DescribeArchSvgData
 */
export const describeArchSvgData: (params: IDescribeArchSvgDataParams) =>
Promise<ResultCommonType<IDescribeArchSvgDataResponse>> = createServiceFunc('DescribeArchSvgData');

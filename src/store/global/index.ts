import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { useSelector } from 'react-redux';
import {
  IScanNodeRiskItem,
  IDescribeArchTaskProgressResult,
  IDescribeArchSyncTaskProgressResult,
} from '@src/service/common-service/index.type';
import {
  IStrategiesItem,
} from '@src/service/node-risk-drawer/index.type';
import {
  RiskFilterTypeEnum,
  InspectionTaskStatusEnum,
  IDriftDetectionTaskEnum,
  NodeInspectionTaskStatusEnum,
  ALL_RISK_TYPE,
} from '@src/constant';

export interface GlobalState {
  nodeRiskDrawerVisible?: boolean;
  selectNodeInfo?: any;
  inspectionDrawerWidth?: number | null;
  supportTaskProductList?: string[] | undefined;
  pluginPropsData?: {
    env: string;
    archInfo: {
      archId: string; // 架构图id
      archTitle: string; // 架构图title
      archVersion: string; // 架构图version
      nodeList?: AppPluginAPI.IResourceList[]
    },
    userName: string;
    uin: string;
  },
  archScanRiskInfo?: { // 架构图巡检风险角标和顶栏信息
    TotalHighRiskCount: number;
    TotalMediumRiskCount: number;
    NodeRiskItems: IScanNodeRiskItem[];
    IsFinish: boolean;
  },
  riskFilterType?: RiskFilterTypeEnum;
  inspectionTaskStatus?: InspectionTaskStatusEnum; // 巡检任务状态
  driftDetectionTaskStatus?: IDriftDetectionTaskEnum; // 漂移检测任务状态
  inspectionTaskData?: IDescribeArchTaskProgressResult;
  driftDetectionTaskData?: IDescribeArchSyncTaskProgressResult | undefined; // 漂移检测任务轮询数据
  isInitTask?: boolean; // 是否是初始化流程还是巡检流程
  reportFinishTime?: string;
  strategies?: IStrategiesItem[]; // 架构图全量巡检策略
  nodeInspectionTaskStatus?: NodeInspectionTaskStatusEnum;
  riskType?: string;
  asyncTriggerScanTask?: any
}

const initialState: GlobalState = {
  nodeRiskDrawerVisible: false,
  selectNodeInfo: undefined,
  inspectionDrawerWidth: null,
  supportTaskProductList: undefined,
  pluginPropsData: undefined,
  archScanRiskInfo: {
    TotalHighRiskCount: 0,
    TotalMediumRiskCount: 0,
    NodeRiskItems: [],
    IsFinish: true,
  },
  riskFilterType: RiskFilterTypeEnum.ALL_RISK, // 架构图顶栏默认风险过滤类型
  inspectionTaskStatus: InspectionTaskStatusEnum.notStarted, // 巡检任务状态枚举
  driftDetectionTaskStatus: IDriftDetectionTaskEnum.notStarted,
  inspectionTaskData: { // 巡检任务轮询数据
    IsFinish: false,
    FinishTime: '',
    NodeTaskStatusList: [],
    Progress: {
      ScannedCount: 0,
      TotalCount: 1,
    },
    LatestScanType: 1,
    CostTime: '',
    BindInsCount: 0,
  },
  isInitTask: false,
  driftDetectionTaskData: undefined,
  reportFinishTime: undefined,
  strategies: [],
  nodeInspectionTaskStatus: NodeInspectionTaskStatusEnum.notStarted,
  riskType: ALL_RISK_TYPE,
  asyncTriggerScanTask: undefined,
};

export const globalState = createSlice({
  name: 'globalState',
  initialState,
  reducers: {
    changeGlobalData: (state, action: PayloadAction<GlobalState>) => {
      Object.assign(state, action.payload);
    },
    resetGlobalData: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const { changeGlobalData, resetGlobalData } = globalState.actions;

export const useGlobalSelector: () => GlobalState = () => useSelector((state: any) => state.globalState);

export default globalState.reducer;

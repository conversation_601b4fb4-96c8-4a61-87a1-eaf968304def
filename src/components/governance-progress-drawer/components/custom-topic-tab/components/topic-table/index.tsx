import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
} from 'react';
import moment from 'moment';
import { Table } from '@tencent/tea-component';
import { Loading } from 'tdesign-react';
import { ChatMarkdown } from '@tencent/cloud-chat-ui';
import rehypeRaw from 'rehype-raw';
import { AuthorTypeEnum } from '@src/constant';
import { describeInsightMessageHandle } from '@src/service/governance-progress-drawer/final-request';
import s from './index.module.scss';

interface IProps {
  list: any[];
  loading: boolean;
  total: number;
  limit?: number;
  offset?: number;
  setQuery?: (updater: (prev: any) => any) => void;
  pluginPropsData: any;
}

const { expandable, pageable, autotip } = Table.addons;

/**
 * 主题表格组件
 * @param props - 组件属性
 * @param props.list - 表格数据
 * @param props.loading - 加载状态
 * @param props.total - 总数据量
 * @param props.limit - 每页数量
 * @param props.offset - 偏移量
 * @param props.setQuery - 更新查询参数的方法
 * @returns React.ReactElement
 */
export default function TopicTable(props: IProps): React.ReactElement {
  const {
    list = [],
    loading,
    total,
    limit = 10,
    offset = 0,
    setQuery,
    pluginPropsData,
  } = props;
  // 当前展开的产品
  const [expandedKeys, setExpandedKeys] = useState([]);
  // 存储每行的聊天详情，key 为记录 ID
  const [chatDetails, setChatDetails] = useState<Record<string, any>>({});
  // 存储每行的加载状态，key 为记录 ID
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {},
  );
  // 用于取消请求的 AbortController 引用
  const abortControllersRef = useRef<Record<string, AbortController>>({});
  // 用于跟踪已请求的记录，避免重复请求
  const requestedRecordsRef = useRef<Set<string>>(new Set());

  // 计算当前页码
  const currentPageIndex = useMemo(
    () => Math.floor(offset / limit) + 1,
    [offset, limit],
  );

  // 处理默认展开
  useEffect(() => {
    if (list?.[0]?.Id) {
      const firstRecord = list[0];
      const recordId = String(firstRecord.Id);
      setExpandedKeys([recordId]);
    }

    // 当列表数据变化时，清理已请求记录的缓存
    requestedRecordsRef.current.clear();
  }, [list]);

  // 处理默认展开行的 ChatDetail 自动获取
  useEffect(() => {
    if (list?.[0]?.Id && expandedKeys.length > 0) {
      const firstRecord = list[0];
      const recordId = String(firstRecord.Id);

      // 如果第一行是展开状态且有 ChatId，自动获取 ChatDetail
      if (
        expandedKeys.includes(recordId)
        && firstRecord.ChatId
        && firstRecord.SessionId
      ) {
        getChatDetail(firstRecord);
      }
    }
  }, [list, expandedKeys]);

  // 处理分页变化
  const handlePagingChange = (query: {
    pageIndex: number;
    pageSize: number;
  }) => {
    if (setQuery) {
      const newOffset = (query.pageIndex - 1) * query.pageSize;
      setQuery((prev) => ({
        ...prev,
        Limit: query.pageSize,
        Offset: newOffset,
      }));
    }
  };

  // 获取聊天详情
  const getChatDetail = useCallback(
    async (record: any, forceRefresh = false) => {
      const recordId = String(record.Id);

      // 如果没有 ChatId 或 SessionId，不需要请求
      if (!record.ChatId || !record.SessionId) {
        return;
      }

      // 如果已经请求过且不是强制刷新，不重复请求
      if (requestedRecordsRef.current.has(recordId) && !forceRefresh) {
        return;
      }

      // 标记为已请求
      requestedRecordsRef.current.add(recordId);

      // 取消之前的请求（如果存在）
      if (abortControllersRef.current[recordId]) {
        abortControllersRef.current[recordId].abort();
      }

      // 创建新的 AbortController
      const abortController = new AbortController();
      abortControllersRef.current[recordId] = abortController;

      // 设置加载状态
      setLoadingStates((prev) => ({ ...prev, [recordId]: true }));

      try {
        const result = await describeInsightMessageHandle({
          env: pluginPropsData.env,
          apiParams: {
            data: {
              ArchId: pluginPropsData.archInfo.archId,
              SessionId: record.SessionId,
              // ChatId: record.ChatId,
            },
          },
          uin: pluginPropsData.uin,
        });

        // 检查请求是否被取消
        if (abortController.signal.aborted) {
          return;
        }

        if (result?.Messages && result.Messages.length > 0) {
          // 存储整个 Messages 数组
          setChatDetails((prev) => ({
            ...prev,
            [recordId]: {
              Messages: result.Messages,
            },
          }));
        }
      } catch (error) {
        // 如果不是因为取消请求导致的错误，则记录错误并移除已请求标记
        if (!abortController.signal.aborted) {
          console.error('获取聊天详情失败:', error);
          requestedRecordsRef.current.delete(recordId);
        }
      } finally {
        // 清除加载状态和 AbortController 引用
        if (!abortController.signal.aborted) {
          setLoadingStates((prev) => ({ ...prev, [recordId]: false }));
        }
        delete abortControllersRef.current[recordId];
      }
    },
    [pluginPropsData],
  );

  // 处理展开状态变化
  const handleExpandedKeysChange = useCallback(
    (keys: string[], { operateRecord }: any) => {
      setExpandedKeys(keys);

      // 如果是展开操作且记录有 ChatId
      if (keys.includes(String(operateRecord.Id)) && operateRecord.ChatId) {
        getChatDetail(operateRecord);
      }
    },
    [getChatDetail],
  );

  // 组件卸载时清理所有请求
  useEffect(
    () => () => {
      Object.values(abortControllersRef.current).forEach((controller) => {
        controller.abort();
      });
    },
    [],
  );

  // 渲染展开内容
  const renderExpandContent = useCallback(
    (record: any) => {
      const recordId = String(record.Id);
      const isLoading = loadingStates[recordId];
      const chatDetail = chatDetails[recordId];

      // 如果没有 ChatId，显示描述
      if (!record.ChatId) {
        return (
          <div style={{ padding: '0 30px', boxSizing: 'border-box' }}>
            {record.Description || '暂无描述'}
          </div>
        );
      }

      // 如果正在加载
      if (isLoading) {
        return (
          <div
            style={{
              padding: '0 30px',
              boxSizing: 'border-box',
              textAlign: 'center',
            }}
          >
            <Loading size="small" text="加载中..." />
          </div>
        );
      }

      // 如果有聊天详情
      if (chatDetail?.Messages) {
        return (
          <div style={{ padding: '0 30px', boxSizing: 'border-box' }}>
            {chatDetail?.Messages?.filter((v) => v?.ContentType !== 'THINK').map((message: any, index: number) => (
              // eslint-disable-next-line implicit-arrow-linebreak
              <div
                // eslint-disable-next-line react/no-array-index-key
                key={`message-${index}`}
                className={s.chat}
                style={{
                  marginBottom:
                    index < chatDetail.Messages.length - 1 ? '16px' : '0',
                }}
              >
                <ChatMarkdown
                  // eslint-disable-next-line react/no-array-index-key
                  content={message?.Content?.trim?.()}
                  markdownProps={{
                    rehypePlugins: [rehypeRaw as any],
                  }}
                />
              </div>
            ))}
          </div>
        );
      }

      // 默认显示描述
      return (
        <div style={{ padding: '0 30px', boxSizing: 'border-box' }}>
          {record.Description || '暂无内容'}
        </div>
      );
    },
    [loadingStates, chatDetails],
  );
  return (
    <div className={s.container}>
      <Table
        verticalTop
        records={list ?? []}
        recordKey="Id"
        className={s.table}
        columns={[
          {
            key: 'Title',
            header: '治理主题',
            render: (record) => (
              <>
                <p>{record?.Title ?? '-'}</p>
              </>
            ),
          },
          {
            key: 'Author',
            header: '作者',
            width: 100,
            // eslint-disable-next-line consistent-return
            render: (record) => {
              if (pluginPropsData.env === 'ISA') {
                return <span>{record?.Author ?? '-'}</span>;
              }
              if (record?.AuthorType === AuthorTypeEnum.ARCHITECT) {
                return <span>{AuthorTypeEnum.ARCHITECT_NAMA}</span>;
              }
              return <span>{record?.Author ?? '-'}</span>;
            },
          },
          {
            key: 'CreateTime',
            header: '发布日期',
            // eslint-disable-next-line max-len
            render: (record) => (
              <span>
                {record?.CreateTime
                  ? moment(record?.CreateTime).format('YYYY-MM-DD')
                  : '-'}
              </span>
            ),
          },
        ]}
        addons={[
          pageable({
            pageIndex: currentPageIndex,
            pageSize: limit,
            recordCount: total,
            onPagingChange: handlePagingChange,
            pageSizeOptions: [10, 20, 50],
          }),
          expandable({
            rowExpand: true,
            // 已经展开的产品
            expandedKeys,
            // 产品展开为消息行
            expand: (record) => record.Description || record.ChatId || null,
            // 发生展开行为时，回调更新展开键值
            onExpandedKeysChange: handleExpandedKeysChange,
            // 只有有描述或有 ChatId 的行允许展开
            shouldRecordExpandable: (record) => Boolean(record.Description || record.ChatId),
            render: renderExpandContent,
          }),
          autotip({
            isLoading: loading,
          }),
        ]}
      />
    </div>
  );
}

/* eslint-disable max-len */
import React, {
  useCallback, useEffect, useMemo, useRef, useState,
} from 'react';
import Up from '@src/assets/svg-component/up-collapse.svg';
import Down from '@src/assets/svg-component/down-collapse.svg';
import { useDispatch } from 'react-redux';
import {
  Tabs,
  TabPanel,
  Table,
  Button,
  Bubble,
  Icon,
} from '@tencent/tea-component';
import { useRiskPanelSelector, changeRiskPanelData } from '@src/store/risk-search-panel';
import { union, isEmpty } from 'lodash';
import {
  describeArchScanRiskInstanceList,
  describeArchScanIgnoreInstanceList,
} from '@src/service/risk-search-drawer/final-request';
import { useGlobalSelector } from '@src/store/global/index';
import TableSearchBox from '@src/components/table-search-box';
import CustomerService from '@src/components/customer-service';
import { RiskColorEnum, Env, EnvEnum } from '@src/constant/index';
import Location from '@src/assets/svg-component/location.svg';
import { simpleMarkdownToHTML, reportEvent } from '@src/utils';
import InstanceIdCopy from '@src/components/instance-id-copy';
import s from './index.module.scss';

interface IProps {
  id: string;
  onIgnore: (data: {
    instanceRegionList: {
      InstanceId: string;
      Region: string;
    }[];
    strategyId: number;
    isCancel?: boolean;
    nodeUuid?: string;
  }) => void;
  data?: any;
  pluginAPI: AppPluginAPI.PluginAPI;
}

/**
 * RiskCollapse组件
 * @param {string} id - Collapse.Panel的 id属性
 * @param {function} onIgnore - 忽略操作
 * @returns React.ReactElement
 */
const RiskCollapse = React.memo((props: IProps): React.ReactElement => {
  const initOffset = 0;
  const initLimit = 10;
  const {
    activeCollapseId,
    strategyTaskItems,
    refreshInstanceListHash,
    positionUuids,
    visible,
    searchBoxValue,
    riskSelectedKeys,
    ignoreSelectedKeys,
  } = useRiskPanelSelector();
  const { pluginPropsData } = useGlobalSelector();
  const firstInit = useRef(true);
  const dispatch = useDispatch();
  const {
    id, onIgnore, data, pluginAPI,
  } = props;
  const [tabs] = useState([
    { id: '有风险', label: '有风险' },
    { id: '已忽略', label: '已忽略' },
  ]);
  const [activeTab, setActiveTab] = useState(tabs[0].id);
  const [searchValue, setSearchValue] = useState([]);
  const [riskList, setRiskList] = useState([]);
  const [riskListLoading, setRiskListLoading] = useState(false);
  const [riskCount, setRiskCount] = useState(0);
  const [riskPageQuery, setRiskPageQuery] = useState({
    Offset: initOffset,
    Limit: initLimit,
  });
  const [ignorePageQuery, setIgnorePageQuery] = useState({
    Offset: initOffset,
    Limit: initLimit,
  });
  const [ignoreList, setIgnoreList] = useState([]);
  const [instanceRegionList, setInstanceRegionList] = useState([]);
  const [ignoreListLoading, setIgnoreListLoading] = useState(false);
  const [ignoreCount, setIgnoreCount] = useState(0);
  const { pageable, selectable, autotip } = Table.addons;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { ConditionId, Conditions: conditions = [] } = data;
  const condition = conditions.find(
    (item) => item?.ConditionId === ConditionId,
  );
  const { Level: level } = condition ?? {};

  const onIgnoreOperation = useCallback(() => {
    onIgnore({
      instanceRegionList,
      strategyId: Number(id),
      isCancel: false,
      nodeUuid: riskList?.[0]?.NodeUuid || ignoreList?.[0]?.NodeUuid,
    });
  }, [instanceRegionList]);

  const onCancelIgnoreOperation = useCallback(() => {
    onIgnore({
      instanceRegionList,
      strategyId: Number(id),
      isCancel: true,
      nodeUuid: riskList?.[0]?.NodeUuid || ignoreList?.[0]?.NodeUuid,
    });
  }, [instanceRegionList]);

  // 合并 filters 和 tagList 的计算，避免异步更新导致的重复请求
  const searchParams = useMemo(
    () => {
      const noTagFilter = searchValue.filter((v) => v?.attr?.key !== 'TagList');
      const tagFilter = searchValue.filter((v) => v?.attr?.key === 'TagList');

      return {
        filters: noTagFilter.map((item) => ({
          Name: item?.attr?.key,
          Values: item.values.map((v) => v.key || v.name),
        })),
        tagList: tagFilter?.[0]?.values.map((v) => ({
          Key: v?.key,
          Value: v?.name,
        })) ?? [],
      };
    },
    [searchValue],
  );

  const fetchRiskInstanceList = useCallback(() => {
    if (activeCollapseId && id && strategyTaskItems.length) {
      const taskItem = strategyTaskItems?.find(
        (item) => `${item.StrategyId}` === id,
      );
      const insFilter = searchBoxValue?.find((item) => item?.attr?.key === 'InsId');
      if (firstInit.current && !isEmpty(insFilter) && isEmpty(searchParams.filters)) {
        return;
      }
      firstInit.current = false;

      if (taskItem?.TaskId) {
        setRiskListLoading(true);
        describeArchScanRiskInstanceList({
          env: pluginPropsData.env as Env,
          uin: pluginPropsData.uin,
          data: {
            MapId: pluginPropsData.archInfo.archId,
            StrategyId: Number(id),
            ...riskPageQuery,
            TaskId: taskItem?.TaskId,
            Filters: searchParams.filters,
            TagList: searchParams.tagList,
          },
        })
          .then((res) => {
            if (res) {
              setRiskList(res?.InstanceItems);
              setRiskCount(res?.Total);
            }
          })
          .finally(() => {
            setRiskListLoading(false);
          });
      }
    }
  }, [activeCollapseId, id, strategyTaskItems, searchParams, riskPageQuery]);

  const fetchIgnoreInstanceList = useCallback(() => {
    if (activeCollapseId && id && strategyTaskItems.length) {
      const taskItem = strategyTaskItems?.find(
        (item) => `${item.StrategyId}` === id,
      );
      const insFilter = searchBoxValue?.find((item) => item?.attr?.key === 'InsId');
      if (firstInit.current && !isEmpty(insFilter) && isEmpty(searchParams.filters)) {
        return;
      }
      firstInit.current = false;

      if (taskItem?.TaskId) {
        setIgnoreListLoading(true);
        describeArchScanIgnoreInstanceList({
          env: pluginPropsData.env as Env,
          uin: pluginPropsData.uin,
          data: {
            MapId: pluginPropsData.archInfo.archId,
            StrategyId: Number(id),
            ...ignorePageQuery,
            TaskId: taskItem?.TaskId,
            Filters: searchParams.filters,
            TagList: searchParams.tagList,
          },
        })
          .then((res) => {
            if (res) {
              setIgnoreList(res?.InstanceItems);
              setIgnoreCount(res?.Total);
            }
          })
          .finally(() => {
            setIgnoreListLoading(false);
          });
      }
    }
  }, [activeCollapseId, id, strategyTaskItems, searchParams, ignorePageQuery]);

  const onLocation = useCallback((nodeUuid: string) => {
    dispatch(
      changeRiskPanelData({
        positionUuids: [nodeUuid],
      }),
    );
  }, []);

  const onUnLocation = useCallback((nodeUuid: string) => {
    const newPositionUuids = positionUuids.filter((item) => item !== nodeUuid);
    dispatch(
      changeRiskPanelData({
        positionUuids: newPositionUuids,
      }),
    );
  }, [positionUuids]);

  const noCondition = !ConditionId;
  const isActive = useMemo(() => activeCollapseId === id, [activeCollapseId, id]);

  useEffect(() => {
    if (positionUuids.length && visible) {
      const nodes = pluginAPI?.getArchNodes?.() ?? {};
      const products = Object.keys(nodes).filter((v) => nodes[v].type === 'SIGMA_PRODUCT_SHAPE');
      pluginAPI.addNodeClass(products, s.grey);
      pluginAPI.removeNodeClass(positionUuids, s.grey);
    } else {
      pluginAPI.removeAllNodeClass();
    }
  }, [positionUuids, visible]);

  useEffect(() => {
    if (
      activeCollapseId
      && id
      && strategyTaskItems.length
      && activeCollapseId === id
    ) {
      fetchRiskInstanceList();
      fetchIgnoreInstanceList();
    }
  }, [
    activeCollapseId,
    id,
    strategyTaskItems,
    refreshInstanceListHash,
    searchParams,
    riskPageQuery,
    ignorePageQuery,
  ]);

  useEffect(() => {
    const insFilter = searchBoxValue.find((item) => item?.attr?.key === 'InsId');
    if (insFilter?.values?.length) {
      setSearchValue((last) => {
        const otherFilter = last.filter((item) => item?.attr?.key !== 'InsId');
        const lastInsFilter = last.find((item) => item?.attr?.key === 'InsId');
        if (!lastInsFilter) {
          return ([...last, insFilter]);
        }
        lastInsFilter.values = union(lastInsFilter.values, insFilter.values);
        return ([...otherFilter, lastInsFilter]);
      });
    }
  }, [searchBoxValue]);
  return (
    <div
      id={id}
      title={data?.Name}
    >
      <div
        className={`${s.riskCollapse} ${s[`level${noCondition ? 0 : level}`]} ${s.hover}`}
        onClick={() => {
          const activeId = isActive ? '' : id;
          if (activeId) {
            reportEvent(pluginPropsData.env as EnvEnum, {
              archId: pluginPropsData.archInfo.archId,
              subUin: pluginPropsData.uin,
              subUinName: pluginPropsData.userName,
              eventType: 'searchExpandRiskItem', // 查找—展开风险项
            });
          }
          dispatch(
            changeRiskPanelData({
              activeCollapseId: activeId,
            }),
          );
          setTimeout(() => {
            document.getElementById(id)?.scrollIntoView({ behavior: 'smooth' });
          }, 50);
        }}
      >
        <span>{data?.Name}</span>
        {isActive ? <Up /> : <Down />}
      </div>
      {
        isActive ? (
          <div style={{ paddingLeft: 10 }}>
            <p style={{ marginTop: 10 }}>
              <span className={s.strong} style={{ width: 40, display: 'inline-block' }}>策略：</span>
              {data?.Desc}
            </p>
            <div style={{ marginTop: 10, display: 'flex' }}>
              <span className={s.strong} style={{ width: 40 }}>条件：</span>
              <span style={{ flex: 1 }}>
                {
                  !noCondition && conditions.map((item, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <span key={`condition-${index}`} style={index > 0 ? { marginTop: 5, display: 'inline-block', width: '100%' } : { display: 'inline-block', width: '100%' }}>
                      <span style={{ color: RiskColorEnum[`level${item.Level}`] }}>
                        {item.LevelDesc}
                      </span>
                      -
                      {' '}
                      {item.Desc}
                      <br />
                    </span>
                  ))
                }
                {noCondition && (
                <p style={{ color: '#DA6C67' }}>
                  当前风险项涉及所有实例被忽略，取消忽略后请尽快发起巡检以更新风险状态
                </p>
                )}
              </span>
            </div>
            <div className={s.advice} style={{ marginTop: 10 }}>
              <p className={s.strong}>优化建议：</p>
              <p
                style={{ marginTop: 10 }}
                // eslint-disable-next-line react/no-danger
                dangerouslySetInnerHTML={{
                  __html: simpleMarkdownToHTML(data?.Repair),
                }}
              />
            </div>
            <div className={s.kefu}>
              <div />
              <CustomerService
                strategyText={data?.Name}
                onClick={() => {
                  reportEvent(pluginPropsData.env as EnvEnum, {
                    archId: pluginPropsData.archInfo.archId,
                    subUin: pluginPropsData.uin,
                    subUinName: pluginPropsData.userName,
                    eventType: 'searchClickAICustomerService', // 查找-唤起智能客服
                    strategyId: id,
                  });
                }}
              />
            </div>
            <Tabs
              tabs={tabs}
              activeId={activeTab}
              onActive={(tab) => {
                setActiveTab(tab.id);
              }}
            >
              {tabs.map((tab) => (
                <TabPanel id={tab.id} key={tab.id}>
                  <div className={s.operation}>
                    {tab.id === tabs[0].id ? (
                      <Button
                        disabled={riskSelectedKeys.length === 0 || pluginPropsData?.env === EnvEnum.ISA}
                        onClick={onIgnoreOperation}
                      >
                        忽略
                      </Button>
                    ) : (
                      <Button
                        disabled={ignoreSelectedKeys.length === 0 || pluginPropsData?.env === EnvEnum.ISA}
                        onClick={onCancelIgnoreOperation}
                      >
                        取消忽略
                      </Button>
                    )}
                    <TableSearchBox
                      value={searchValue}
                      hasLevelFilter={false}
                      onChange={(v) => {
                        setSearchValue(v);
                      }}
                    />
                  </div>
                  <Table
                    verticalTop
                    className={s.table}
                    records={tab.id === tabs[0].id ? riskList : ignoreList}
                    recordKey="InstanceId"
                    bordered
                    style={{
                      marginTop: 10,
                    }}
                    columns={[
                      {
                        key: 'InstanceId',
                        header: 'ID/实例名',
                        width: 130,
                        render: (record) => (
                          <>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <div
                                className={`${s[`levelDot${record.Level}`]} ${s.levelDot}`}
                              />
                              <div style={{ width: 130 }}>
                                <InstanceIdCopy instanceId={record.InstanceId} url={record.InstanceUrl} />
                              </div>
                            </div>
                          </>
                        ),
                      },
                      {
                        key: 'NodeUuid',
                        header: '定位',
                        width: 80,
                        render: (record) => (!positionUuids.includes(record.NodeUuid) ? (
                          <div className={`${s.operationHover} ${s.location}`} onClick={() => onLocation(record.NodeUuid)}>
                            <Location />
                            {' '}
                            定位
                          </div>
                        ) : (
                          <div className={`${s.operationHover} ${s.unlocation}`} onClick={() => onUnLocation(record.NodeUuid)}>
                            停止定位
                          </div>
                        )),
                      },
                      {
                        key: 'InstanceTags',
                        header: '标签',
                        width: 50,
                        render: (record) => {
                          try {
                            const tag = record.InstanceTags;
                            const tagArray = tag ? JSON.parse(tag) : [];
                            return tagArray.length > 0 ? (
                              <Bubble
                                arrowPointAtCenter
                                placement="top"
                                content={
                                  <div>
                                    {tagArray.map((tag) => (
                                      <div key={tag.Key}>
                                        <span>
                                          {tag.Key}
                                          ：
                                        </span>
                                        <span>{tag.Value}</span>
                                      </div>
                                    ))}
                                  </div>
                          }
                              >
                                <Icon type="tag" />
                              </Bubble>
                            ) : (
                              '-'
                            );
                          } catch (error) {
                            return '-';
                          }
                        },
                      },
                      {
                        key: 'ClaimPerson',
                        header: '跟进人',
                        render: (record) => <span>{record.ClaimPerson || ''}</span>,
                        width: 80,
                      },
                      {
                        key: 'operation',
                        header: '操作',
                        width: 80,
                        render: (record) => (
                          <>
                            {tab.id === tabs[0].id ? (
                              <Button
                                type="link"
                                disabled={pluginPropsData?.env === EnvEnum.ISA}
                                onClick={() => {
                                  reportEvent(pluginPropsData.env as EnvEnum, {
                                    archId: pluginPropsData.archInfo.archId,
                                    subUin: pluginPropsData.uin,
                                    subUinName: pluginPropsData.userName,
                                    eventType: 'searchClickInstanceIgnore', // 查找-点击实例忽略
                                    instanceId: record.InstanceId,
                                    strategyId: id,
                                  });
                                  onIgnore({
                                    instanceRegionList: [
                                      {
                                        InstanceId: record.InstanceId,
                                        Region: record.Region,
                                      },
                                    ],
                                    strategyId: Number(id),
                                    isCancel: false,
                                    nodeUuid: record.NodeUuid,
                                  });
                                }}
                                className={`${s.operationBtn} ${s.operationHover}`}
                              >
                                忽略
                              </Button>
                            ) : (
                              <Button
                                type="link"
                                disabled={pluginPropsData?.env === EnvEnum.ISA}
                                onClick={() => {
                                  reportEvent(pluginPropsData.env as EnvEnum, {
                                    archId: pluginPropsData.archInfo.archId,
                                    subUin: pluginPropsData.uin,
                                    subUinName: pluginPropsData.userName,
                                    eventType: 'searchClickUnignore', // 查找-点击取消忽略
                                    instanceId: record.InstanceId,
                                    strategyId: id,
                                  });
                                  onIgnore({
                                    instanceRegionList: [
                                      {
                                        InstanceId: record.InstanceId,
                                        Region: record.Region,
                                      },
                                    ],
                                    strategyId: Number(id),
                                    isCancel: true,
                                    nodeUuid: record.NodeUuid,
                                  });
                                }}
                                className={`${s.operationBtn} ${s.operationHover}`}
                              >
                                取消忽略
                              </Button>
                            )}
                          </>
                        ),
                      },
                    ]}
                    addons={[
                      pageable({
                        recordCount: tab.id === tabs[0].id ? riskCount : ignoreCount,
                        pageIndex: tab.id === tabs[0].id ? (riskPageQuery.Offset + riskPageQuery.Limit) / riskPageQuery.Limit : (ignorePageQuery.Offset + ignorePageQuery.Limit) / ignorePageQuery.Limit,
                        pageSizeOptions: [10, 20, 50, 100, 200],
                        onPagingChange: (pagingQuery) => {
                          if (tab.id === tabs[0].id) {
                            setRiskPageQuery({
                              ...riskPageQuery,
                              Limit: pagingQuery.pageSize,
                              Offset: (pagingQuery.pageIndex - 1) * pagingQuery.pageSize,
                            });
                          } else {
                            setIgnorePageQuery({
                              ...ignorePageQuery,
                              Limit: pagingQuery.pageSize,
                              Offset: (pagingQuery.pageIndex - 1) * pagingQuery.pageSize,
                            });
                          }
                        },
                      }),
                      selectable({
                        value:
                    tab.id === tabs[0].id
                      ? riskSelectedKeys
                      : ignoreSelectedKeys,
                        onChange: (keys) => {
                          const list = tab.id === tabs[0].id ? riskList : ignoreList;
                          const instanceRegionList = list
                            .filter((item) => keys.includes(item.InstanceId))
                            .map((item) => ({
                              InstanceId: item.InstanceId,
                              Region: item.Region,
                            }));
                          setInstanceRegionList(instanceRegionList);
                          if (tab.id === tabs[0].id) {
                            dispatch(
                              changeRiskPanelData({
                                riskSelectedKeys: keys,
                              }),
                            );
                          } else {
                            dispatch(
                              changeRiskPanelData({
                                ignoreSelectedKeys: keys,
                              }),
                            );
                          }
                        },
                      }),
                      autotip({
                        isLoading:
                    tab.id === tabs[0].id ? riskListLoading : ignoreListLoading,
                      }),
                    ]}
                  />
                </TabPanel>
              ))}
            </Tabs>
          </div>
        ) : null
      }
    </div>
  );
});
RiskCollapse.displayName = 'RiskCollapse';
export default RiskCollapse;

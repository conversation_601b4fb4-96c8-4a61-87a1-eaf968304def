/* eslint-disable import/prefer-default-export */
import { getAppid } from '@src/utils/index';
import { AuthorTypeEnum } from '@src/constant';
import {
  createSubscriptionEmailV2,
  describeRiskInstancesInMap,
  updateInstanceToClaimedStatusInMap,
  updateInstanceToIgnoredStatusInMap,
  updateSubscriptionEmailV2,
  describeRiskManageSubjectList,
  describeRiskManageInstanceList,
  createRiskManageInstance,
  updateRiskManageSubject,
  deleteRiskManageSubject,
  describeInsightMessage,
  describeArchSvgData,
} from './index';
import {
  ICreateSubscriptionEmailV2Params,
  IDescribeRiskInstancesInMapParams,
  IDescribeRiskInstancesInMapResult,
  IupdateInstanceToClaimedStatusInMapParams,
  IUpdateInstanceToIgnoredStatusInMapParams,
  IUpdateSubscriptionEmailV2Params,
  IDescribeRiskManageSubjectListParams,
  IDescribeRiskManageSubjectListResponse,
  IDescribeRiskManageInstanceListParams,
  IDescribeRiskManageInstanceListResponse,
  ICreateRiskManageInstanceParams,
  IUpdateRiskManageSubjectParams,
  IUpdateRiskManageSubjectResponse,
  IDeleteRiskManageSubjectParams,
  IDescribeInsightMessageParams,
  IDescribeInsightMessageResponse,
  IDescribeArchSvgDataParams,
  IDescribeArchSvgDataResponse,
} from './index.type';

import {
  createSubscriptionEmailV2Admin,
  describeRiskInstancesInMapAdmin,
  updateInstanceToClaimedStatusInMapAdmin,
  updateInstanceToIgnoredStatusInMapAdmin,
  updateSubscriptionEmailV2Admin,
  describeRiskManageSubjectListAdmin,
  describeRiskManageInstanceListAdmin,
  createRiskManageInstanceAdmin,
  updateRiskManageSubjectAdmin,
  deleteRiskManageSubjectAdmin,
  describeInsightMessageAdmin,
  describeArchSvgDataAdmin,
} from './index-admin';

export const describeRiskInstancesInMapHandle = async (params: {
  env: string;
  apiParams: IDescribeRiskInstancesInMapParams,
  uin?: string,
}): Promise<IDescribeRiskInstancesInMapResult | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeRiskInstancesInMap(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeRiskInstancesInMapAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const createSubscriptionEmailV2Handle = async (params: {
  env: string;
  apiParams: ICreateSubscriptionEmailV2Params,
  uin?: string,
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await createSubscriptionEmailV2(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await createSubscriptionEmailV2Admin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs ?? await Promise.reject(new Error(''));
  } catch (error) {
    throw new Error(error);
  }
};

export const updateSubscriptionEmailV2Handle = async (params: {
  env: string;
  apiParams: IUpdateSubscriptionEmailV2Params,
  uin?: string,
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await updateSubscriptionEmailV2(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await updateSubscriptionEmailV2Admin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs ?? await Promise.reject(new Error(''));
  } catch (error) {
    throw new Error(error);
  }
};

export const updateInstanceToClaimedStatusInMapHandle = async (params: {
  env: string;
  apiParams: IupdateInstanceToClaimedStatusInMapParams,
  uin?: string,
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await updateInstanceToClaimedStatusInMap(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await updateInstanceToClaimedStatusInMapAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs ?? await Promise.reject(new Error(''));
  } catch (error) {
    throw new Error(error);
  }
};

export const updateInstanceToIgnoredStatusInMapHandle = async (params: {
  env: string;
  apiParams: IUpdateInstanceToIgnoredStatusInMapParams,
  uin?: string,
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await updateInstanceToIgnoredStatusInMap(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await updateInstanceToIgnoredStatusInMapAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const getRiskManageSubjectList = async (params: {
  env: string;
  apiParams: IDescribeRiskManageSubjectListParams,
  uin?: string,
}): Promise<IDescribeRiskManageSubjectListResponse | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeRiskManageSubjectList(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeRiskManageSubjectListAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const getRiskManageInstanceList = async (params: {
  env: string;
  apiParams: IDescribeRiskManageInstanceListParams,
  uin?: string,
}): Promise<IDescribeRiskManageInstanceListResponse | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeRiskManageInstanceList(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeRiskManageInstanceListAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const createRiskManageInstanceHandle = async (params: {
  env: string;
  apiParams: ICreateRiskManageInstanceParams,
  uin?: string,
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await createRiskManageInstance(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await createRiskManageInstanceAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs ?? await Promise.reject(new Error(''));
  } catch (error) {
    throw new Error(error);
  }
};

export const updateRiskManageSubjectHandle = async (params: {
  env: string;
  apiParams: IUpdateRiskManageSubjectParams,
  uin?: string,
}): Promise<IUpdateRiskManageSubjectResponse | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      apiParams.data.AuthorType = AuthorTypeEnum.NORMAL;
      const result = await updateRiskManageSubject(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await updateRiskManageSubjectAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
        AuthorType: AuthorTypeEnum.ARCHITECT,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs ?? await Promise.reject(new Error(''));
  } catch (error) {
    throw new Error(error);
  }
};

export const deleteRiskManageSubjectHandle = async (params: {
  env: string;
  apiParams: IDeleteRiskManageSubjectParams,
  uin?: string,
}): Promise<unknown | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await deleteRiskManageSubject(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await deleteRiskManageSubjectAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs ?? await Promise.reject(new Error(''));
  } catch (error) {
    throw new Error(error);
  }
};

export const describeInsightMessageHandle = async (params: {
  env: string;
  apiParams: IDescribeInsightMessageParams,
  uin?: string,
}): Promise<IDescribeInsightMessageResponse | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeInsightMessage(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeInsightMessageAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

export const describeArchSvgDataHandle = async (params: {
  env: string;
  apiParams: IDescribeArchSvgDataParams,
  uin?: string,
}): Promise<IDescribeArchSvgDataResponse | undefined> => {
  try {
    const {
      env,
      apiParams,
      uin,
    } = params;
    let rs;
    if (env === 'CONSOLE') {
      const result = await describeArchSvgData(apiParams);
      if (!result?.data?.Response?.Error) {
        rs = result.data.Response;
      }
    } else {
      const result = await describeArchSvgDataAdmin({
        ...apiParams.data,
        AppId: getAppid(),
        CustomerAppId: getAppid(),
        Uin: uin,
        SubAccountUin: uin,
      });
      if (!result?.Response?.Error) {
        rs = result.Response;
      }
    }
    return rs;
  } catch (error) {
    throw new Error(error);
  }
};

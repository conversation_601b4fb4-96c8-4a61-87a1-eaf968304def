// eslint-disable-next-line import/prefer-default-export, @typescript-eslint/naming-convention
export const reportVersion = 'v4';

export type Env = 'CONSOLE' | 'ISA';

export const ALL_RISK_TYPE = '0'; // 全部风险类型

export enum EnvEnum {
  CONSOLE = 'CONSOLE',
  ISA = 'ISA',
}

export enum RiskFilterTypeEnum { //  架构图风险过滤类型 默认为全部风险
  ALL_RISK = 'ALL_RISK',
  HIGH_RISK_ONLY = 'HIGH_RISK_ONLY',
  MID_RISK_ONLY = 'MID_RISK_ONLY',
}

export enum InspectionTaskStatusEnum { // 巡检任务状态
  notStarted = 'notStarted',
  inspectioning = 'inspectioning',
  inspectionCompleted = 'inspectionCompleted',
}

export enum NodeInspectionTaskStatusEnum { // 巡检任务状态
  notStarted = 'notStarted',
  inspectioning = 'inspectioning',
  inspectionCompleted = 'inspectionCompleted',
}

export enum IDriftDetectionTaskEnum { // 偏移检测任务状态
  notStarted = 'notStarted', // 未开始
  driftDetectioning = 'driftDetectioning', // 检测中
  driftDetectioned = 'driftDetectioned' // 检测完成
}

export enum RiskTabEnum {
  risky = 'risky',
  claimed = 'claimed',
  ignored = 'ignored',
  noRisk = 'noRisk'
}

export enum RiskProgressEnum {
  instance = 'instance',
  customTopic = 'customTopic',
}

export enum RiskLevelEnum {
  highRisk = 3,
  mediumRisk = 2
}

export enum RiskColorEnum {
  level3 = '#e54545',
  level2 = '#ff7200',
  level0 = '#dee1e6',
}

export enum UpdateInstanceToClaimedOperateEnum {
  add = 'add',
  delete = 'delete'
}

export enum ShapeTypeEnum {
  SIGMA_RECTANGLE_SHAPE = 'SIGMA_RECTANGLE_SHAPE',
  SIGMA_BLOCK_SHAPE = 'SIGMA_BLOCK_SHAPE',
  SIGMA_TEXTLABEL_SHAPE = 'SIGMA_TEXTLABEL_SHAPE',
  SIGMA_ICON_SHAPE = 'SIGMA_ICON_SHAPE',
  SIGMA_IMAGE_SHAPE = 'SIGMA_IMAGE_SHAPE',
  SIGMA_AREA_SHAPE = 'SIGMA_AREA_SHAPE',
  SIGMA_AUTOSCALING_SHAPE = 'SIGMA_AUTOSCALING_SHAPE',
  SIGMA_AVAILABILITYZONE_SHAPE = 'SIGMA_AVAILABILITYZONE_SHAPE',
  SIGMA_PRODUCT_SHAPE = 'SIGMA_PRODUCT_SHAPE',
  SIGMA_LINE_SHAPE = 'SIGMA_LINE_SHAPE',
  SIGMA_CIRCLE_SHAPE = 'SIGMA_CIRCLE_SHAPE',
  SIGMA_SECURITY_GROUP_SHAPE = 'SIGMA_SECURITY_GROUP_SHAPE',
  SIGMA_SUBNET_SHAPE = 'SIGMA_SUBNET_SHAPE',
  SIGMA_VPC_SHAPE = 'SIGMA_VPC_SHAPE',
  SIGMA_GROUP_SHAPE = 'SIGMA_GROUP_SHAPE',
  SIGMA_SECURITY_SHAPE = 'SIGMA_SECURITY_SHAPE',
  SIGMA_BASE_GROUP_SHAPE = 'SIGMA_BASE_GROUP_SHAPE',
  SIGMA_REMARK_NOTE_SHAPE = 'SIGMA_REMARK_NOTE_SHAPE',
  SIGMA_CCN_SHAPE = 'SIGMA_CCN_SHAPE',
  SIGMA_TKE_SHAPE = 'SIGMA_TKE_SHAPE',
}

export enum UrlParamsEnum {
  FROM = 'from',
  INSPECTION_EMAIL = 'inspection-email',
}

export enum GovernanceTabEvents {
  SWITCH_GOVERNANCE_TAB = 'switchGovernanceTab'
}

export enum AuthorTypeEnum {
  NORMAL = 1,
  ARCHITECT = 2,
  ARCHITECT_NAMA = '腾讯云架构师'
}

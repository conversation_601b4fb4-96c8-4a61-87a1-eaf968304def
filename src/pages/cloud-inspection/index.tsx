/* eslint-disable @typescript-eslint/no-misused-promises */
import React, { useRef } from 'react';
import NodeRiskDrawer from '@src/components/node-risk-drawer';
import InspectionSettingsModal from '@src/components/inspection-settings-modal';
import GlobalReport from '@src/components/global-report';
import NodeReport from '@src/components/node-report';
import RiskPanel from '@src/components/risk-search-panel';
import ChatBiDrawer from '@src/components/chat-bi-drawer';
import { useGlobalSelector } from '@src/store/global/index';
import useShapesBar from '@src/hooks/useShapesBar';
import useOperationsToolBar from '@src/hooks/useOperationsToolBar';
import useReport from '@src/hooks/useReport';
import useInit from '@src/hooks/useInit';
import useInitFetchData from '@src/hooks/useInitFetchData';
import useUrlJump from '@src/hooks/useUrlJump';
import useChatBiInit from '@src/hooks/useChatBiInit';
import useCreateSmartIsland from '@src/hooks/useCreateSmartIsland';
import GovernanceProgressDrawer from '@src/components/governance-progress-drawer';
import useProgressMainTabSwitch from '@src/hooks/useProgressMainTabSwitch';

interface IRootProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

export default function CloudInspection(props: IRootProps): React.ReactElement {
  const {
    pluginAPI,
  } = props;

  const { pluginPropsData } = useGlobalSelector();
  const reportBtnConfigContentRef = useRef(null);
  // eslint-disable-next-line no-spaced-func
  const reportRef = React.useRef<{
    // eslint-disable-next-line func-call-spacing
    resetScanInfo: () => void,
  }>(null);

  const { createArchScanReportFile, stopReportGeneration } = useReport({ pluginAPI });
  useProgressMainTabSwitch();
  useInitFetchData({ pluginAPI });
  useInit({ pluginAPI, createArchScanReportFile });
  useShapesBar({ pluginAPI });
  useOperationsToolBar({
    pluginAPI, createArchScanReportFile, reportBtnConfigContentRef, stopReportGeneration,
  });
  useUrlJump({ pluginAPI });
  useChatBiInit({ pluginAPI });
  useCreateSmartIsland({ pluginAPI });
  if (!pluginPropsData) {
    return null;
  }

  return (
    <>
      <NodeRiskDrawer pluginAPI={pluginAPI} />
      <InspectionSettingsModal />
      <GlobalReport
        createArchScanReportFile={createArchScanReportFile}
        stopReportGeneration={stopReportGeneration}
        pluginAPI={pluginAPI}
        ref={reportRef}
        reportBtnConfigContentRef={reportBtnConfigContentRef}
      />
      <RiskPanel pluginAPI={pluginAPI} createArchScanReportFile={createArchScanReportFile} />
      <NodeReport createArchScanReportFile={createArchScanReportFile} pluginAPI={pluginAPI} ref={reportRef} />
      <ChatBiDrawer />
      <GovernanceProgressDrawer pluginAPI={pluginAPI} />
    </>
  );
}

import React, { useState } from 'react';
import {
  Tabs, TabPanel,
} from '@tencent/tea-component';
import { useGlobalSelector } from '@src/store/global/index';
import {
  EnvEnum,
  RiskProgressEnum,
} from '@src/constant/index';
import { reportEvent } from '@src/utils';
import RiskInstanceTable from '@src/components/governance-progress-drawer/components/risk-instance-table';
import CustomTopicTab from '@src/components/governance-progress-drawer/components/custom-topic-tab';
// eslint-disable-next-line max-len
import { useGovernanceProgressDrawerStateSelector, changeGovernanceProgressDrawerState } from '@src/store/governance-progress-drawer/index';
import { useDispatch } from 'react-redux';
import s from './index.module.scss';

/**
 * 治理进展抽屉
 * @returns
 */
export default function ProgressMain(): React.ReactElement {
  const {
    selectNodeInfo,
    pluginPropsData,
  } = useGlobalSelector();
  const { activeTabId } = useGovernanceProgressDrawerStateSelector();
  const dispatch = useDispatch();
  const [tabs] = useState([
    { id: RiskProgressEnum.instance, label: '风险实例' },
    { id: RiskProgressEnum.customTopic, label: '自定主题' },
  ]);

  const eventType = {
    [RiskProgressEnum.instance]: 'clickRiskProgressInstanceTab',
  };

  const tableMap: any = {
    [RiskProgressEnum.instance]: <RiskInstanceTable />,
    [RiskProgressEnum.customTopic]: <CustomTopicTab />,
  };

  return (
    <div className={s['node-risk-drawer-content']}>
      <div className={s['node-risk-drawer-header']}>该架构图各风险实例分配跟进情况，风险治理主题，您可以随时查看详情并执行操作。</div>
      <div className={s['node-risk-tabs']}>
        <Tabs
          tabs={tabs}
          activeId={activeTabId}
          onActive={(tab) => {
            dispatch(changeGovernanceProgressDrawerState({ activeTabId: tab.id }));
            reportEvent(pluginPropsData.env as EnvEnum, {
              archId: pluginPropsData?.archInfo?.archId ?? '',
              nodeId: selectNodeInfo?.key ?? '',
              nodeName: selectNodeInfo?.name ?? '',
              subUin: pluginPropsData?.uin ?? '',
              subUinName: pluginPropsData?.userName ?? '',
              eventType: eventType[tab.id] ?? '',
            });
          }}
        >
          {tabs.map((tab) => (
            <TabPanel id={tab.id} key={tab.id}>
              {
                tableMap[tab.id]
              }
            </TabPanel>
          ))}
        </Tabs>
      </div>
    </div>
  );
}

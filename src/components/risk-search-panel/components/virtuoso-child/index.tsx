import React from 'react';
import { cloneDeep, orderBy } from 'lodash';
import { useRiskPanelSelector } from '@src/store/risk-search-panel';
import {
  Status,
} from '@tencent/tea-component';
import RiskCollapse from '../risk-collapse';

// eslint-disable-next-line react/prop-types, max-len
const Child: React.FC<{ strategies: any; onIgnore: (data: any) => void; loading: boolean; pluginAPI: AppPluginAPI.PluginAPI }> = React.memo(({
  // eslint-disable-next-line react/prop-types
  strategies, onIgnore, loading, pluginAPI,
}) => {
  // const scrollerRef = useRef(null);
  const newMap = new Map();
  const { strategyTaskItems } = useRiskPanelSelector();
  strategyTaskItems?.forEach((item) => {
    newMap.set(item.StrategyId, item);
  });
  // eslint-disable-next-line react/prop-types
  const items = strategies ?? [];
  const strategyTaskItemsIds = strategyTaskItems?.map((item) => `${item.StrategyId}`);
  const data = items.filter((item) => strategyTaskItemsIds?.includes(`${item.StrategyId}`));
  const newData = cloneDeep(data);
  newData.forEach((item) => {
    const condition = newMap.get(item.StrategyId);
    // eslint-disable-next-line no-param-reassign
    item.ConditionId = condition?.ConditionId;
    // eslint-disable-next-line no-param-reassign
    item.Level = condition?.Level;
  });
  const sortedData = orderBy(newData, ['Level', 'Name'], ['desc', 'desc']);

  if (loading) {
    return (
      <Status
        icon="loading"
        title="加载中..."
        size="s"
      />
    );
  }
  return (
    <>
      {sortedData.length > 0
        ? <div style={{ height: `${window.innerHeight - 368}px`, overflow: 'auto' }}>
          {sortedData.map((item, index) => (
            <div key={item.StrategyId} style={{ marginTop: 5 }} id={`collapse-${sortedData[index].StrategyId}`}>
              <RiskCollapse
                pluginAPI={pluginAPI}
                data={sortedData[index]}
                onIgnore={onIgnore}
                id={`${sortedData[index].StrategyId}`}
              />
            </div>
          ))}
          </div>
        : strategyTaskItems !== null && <Status
            icon="blank"
            title="暂无数据"
            size="s"
        />}
    </>
  );
});
Child.displayName = 'Child';
export default Child;

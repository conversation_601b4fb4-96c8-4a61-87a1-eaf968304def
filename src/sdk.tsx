import React from 'react';
import { app } from '@tencent/tea-app';
import { store } from '@src/store';
import { changeGlobalData, resetGlobalData } from '@src/store/global/index';
import { changeGovernanceProgressDrawerState } from '@src/store/governance-progress-drawer';
import { resetInspectionsSettingsModalState } from '@src/store/inspection-settings-modal/index';
import { resetNodeRiskDrawerState, changeNodeRiskDrawerState } from '@src/store/node-risk-drawer/index';
import { resetReportData } from '@src/store/report/index';
import { chatBiCallBacks } from '@src/utils/caching';
import {
  resetRiskPanelData,
  changeRiskPanelData,
} from '@src/store/risk-search-panel/index';
import { message } from '@tencent/tea-component';
import { ShapeTypeEnum, EnvEnum } from '@src/constant/index';
import { SPECIAL_PRODUCT_ARRAY, SpecialProductMap } from '@src/constant/config';
import { reportEvent } from '@src/utils/index';
import _ from 'lodash';
import Root from './index';
import '@tencent/tea-component/dist/tea.css';
import '@tencent/cloud-chat-ui/dist/assets/style.css';
import './index.css';
import './i18n';

// 注册 SDK 入口，提供 SDK 工厂方法
app.sdk.register('cloud-inspection-sdk', () => ({
  init(props: AppPluginAPI.PluginAPI) {
    const { dispatch } = store; // 直接从 store 获取 dispatch
    dispatch(changeGlobalData({
      pluginPropsData: {
        env: props.env,
        archInfo: props.archInfo,
        userName: props.userName,
        uin: props.uin,
      },
    }));
    props.setSlotComponent(<Root pluginAPI={props} />);
    return {
      onShapeClick: ({ node, event }) => {
        const nodeCopy = _.cloneDeep(node);
        // eslint-disable-next-line no-param-reassign
        delete nodeCopy?.customize?.shapeBarParam;
        // eslint-disable-next-line no-param-reassign
        delete nodeCopy?.stickyUnits;
        const {
          nodeList,
        } = props.archInfo;
        const {
          globalState,
        } = store.getState();
        const {
          supportTaskProductList,
        } = globalState;
        reportEvent(props.env as EnvEnum, {
          archId: props.archInfo.archId,
          nodeId: nodeCopy.key,
          nodeName: nodeCopy.name,
          subUin: props.uin,
          subUinName: props.userName,
          eventType: 'clickNodeEvent',
        });
        // 判断当前点击的节点是否支持巡检任务
        if (!supportTaskProductList) {
          return;
        }
        if (supportTaskProductList?.length === 0) {
          return;
        }
        if (SPECIAL_PRODUCT_ARRAY.includes(nodeCopy.name)) {
          const filter = [
            {
              attr: {
                key: 'Product',
                name: '云产品',
              },
              values: [
                {
                  key: SpecialProductMap[nodeCopy.name],
                  name: SpecialProductMap[nodeCopy.name],
                },
              ],
              _key: 0,
            },
          ];
          dispatch(changeRiskPanelData({
            visible: true,
            searchBoxValue: filter,
          }));
          dispatch(changeGlobalData({
            nodeRiskDrawerVisible: false,
          }));
          return;
        }
        if (!supportTaskProductList?.map((item) => item.toLowerCase()).includes(nodeCopy.name.toLowerCase())) {
          if (node?.type === ShapeTypeEnum.SIGMA_PRODUCT_SHAPE) {
            dispatch(changeGlobalData({
              nodeRiskDrawerVisible: false,
              selectNodeInfo: nodeCopy,
            }));
            message.error({
              content: '暂不支持对该类型节点进行巡检',
            });
          }
          return;
        }
        const supportBindShapeArray = props?.getSupportBindShapeArray?.();
        if (supportBindShapeArray?.length === 0) {
          return;
        }
        if (!supportBindShapeArray?.includes(nodeCopy.name)) {
          // 如果该节点不支持资源绑定直接reture
          return;
        }
        const nodeItem = nodeList.find((item) => item.DiagramId === nodeCopy.key);
        if (nodeItem.hasResource === undefined) {
          return;
        }
        if (nodeItem.hasResource === false) {
          dispatch(changeGlobalData({
            nodeRiskDrawerVisible: false,
            selectNodeInfo: nodeCopy,
          }));
          message.error({
            content: '该节点暂未绑定资源。您需要先配置节点并绑定资源，重新巡检之后才能查看该节点的风险。',
          });
          return;
        }
        chatBiCallBacks?.onShapeClick?.({
          node,
          event,
        });
        chatBiCallBacks?.changeChatVisibleFlag?.(false);
        dispatch(changeGlobalData({
          nodeRiskDrawerVisible: true,
          selectNodeInfo: nodeCopy,
        }));
        dispatch(changeGovernanceProgressDrawerState({
          visible: false,
        }));
        dispatch(
          changeRiskPanelData({
            visible: false,
          }),
        );
        dispatch(changeNodeRiskDrawerState({
          riskTablePage: 1,
          claimedTabelPage: 1,
          noRiskTablePage: 1,
          ignoredTablePage: 1,
        }));
      },
    };
  },
  destroy() {
    const { dispatch } = store;
    dispatch(resetGlobalData());
    dispatch(resetInspectionsSettingsModalState());
    dispatch(resetNodeRiskDrawerState());
    dispatch(resetReportData());
    dispatch(resetRiskPanelData());
    chatBiCallBacks?.changeChatVisibleFlag?.(false);
  },
}));
